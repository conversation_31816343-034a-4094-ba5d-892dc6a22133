# Augment GitHub MCP 配置脚本
# 使用方法: .\setup-github-mcp.ps1 -GitHubToken "your_github_pat_here"

param(
    [Parameter(Mandatory=$true)]
    [string]$GitHubToken
)

Write-Host "🔧 配置 Augment GitHub MCP 工具..." -ForegroundColor Green

# 设置环境变量
[Environment]::SetEnvironmentVariable("GITHUB_PERSONAL_ACCESS_TOKEN", $GitHubToken, "User")
[Environment]::SetEnvironmentVariable("GITHUB_TOKEN", $GitHubToken, "User")

Write-Host "✅ GitHub Token 已设置为用户环境变量" -ForegroundColor Green

# 创建 Augment MCP 配置目录
$augmentConfigDir = "$env:USERPROFILE\.augment"
if (-not (Test-Path $augmentConfigDir)) {
    New-Item -ItemType Directory -Path $augmentConfigDir -Force
    Write-Host "📁 创建 Augment 配置目录: $augmentConfigDir" -ForegroundColor Yellow
}

# 创建 MCP 配置文件
$mcpConfigPath = "$augmentConfigDir\mcp.json"
$mcpConfig = @{
    servers = @{
        github = @{
            url = "https://api.githubcopilot.com/mcp/"
            headers = @{
                Authorization = "Bearer $GitHubToken"
                "Content-Type" = "application/json"
            }
        }
    }
} | ConvertTo-Json -Depth 3

$mcpConfig | Out-File -FilePath $mcpConfigPath -Encoding UTF8
Write-Host "📝 MCP 配置文件已创建: $mcpConfigPath" -ForegroundColor Green

Write-Host "`n🎉 GitHub MCP 配置完成！" -ForegroundColor Cyan
Write-Host "请重启 Augment 以使配置生效。" -ForegroundColor Yellow

# 显示配置内容
Write-Host "`n📋 配置内容预览:" -ForegroundColor Blue
Get-Content $mcpConfigPath
